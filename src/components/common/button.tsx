'use client';

import { motion, type HTMLMotionProps } from 'framer-motion';
import React from 'react';
import { tv, type VariantProps } from 'tailwind-variants';

/**
 * Button component variants using tailwind-variants
 */
export const buttonVariants = tv({
  base: [
    'inline-flex items-center justify-center gap-2 rounded-lg font-medium transition-all duration-200',
    'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2',
    'disabled:pointer-events-none disabled:opacity-50',
    'relative overflow-hidden',
  ],
  variants: {
    variant: {
      primary: [
        'bg-brand-mint text-white shadow-sm',
        'hover:bg-brand-sage hover:shadow-md',
        'active:bg-brand-mint/90',
        'dark:bg-brand-mint dark:text-white',
        'dark:hover:bg-brand-sage',
      ],
      secondary: [
        'bg-surface border border-border text-primary shadow-sm',
        'hover:bg-elevated hover:shadow-md',
        'active:bg-surface/80',
        'dark:bg-surface dark:border-border dark:text-primary',
        'dark:hover:bg-elevated',
      ],
      outline: [
        'border border-brand-mint text-brand-mint bg-transparent',
        'hover:bg-brand-mint hover:text-white hover:shadow-md',
        'active:bg-brand-mint/90 active:text-white',
        'dark:border-brand-mint dark:text-brand-mint',
        'dark:hover:bg-brand-mint dark:hover:text-white',
      ],
      ghost: [
        'text-brand-mint bg-transparent',
        'hover:bg-brand-mint/10 hover:text-brand-mint',
        'active:bg-brand-mint/20',
        'dark:text-brand-mint',
        'dark:hover:bg-brand-mint/10',
      ],
      destructive: [
        'bg-brand-coral text-white shadow-sm',
        'hover:bg-brand-coral/90 hover:shadow-md',
        'active:bg-brand-coral/80',
        'dark:bg-brand-coral dark:text-white',
        'dark:hover:bg-brand-coral/90',
      ],
      link: [
        'text-brand-mint underline-offset-4',
        'hover:underline',
        'active:text-brand-mint/80',
        'dark:text-brand-mint',
      ],
    },
    size: {
      sm: 'h-8 px-3 text-sm',
      md: 'h-10 px-4 text-sm',
      lg: 'h-11 px-6 text-base',
      xl: 'h-12 px-8 text-base',
      icon: 'h-10 w-10 p-0',
    },
    fullWidth: {
      true: 'w-full',
    },
  },
  defaultVariants: {
    variant: 'primary',
    size: 'md',
  },
});

/**
 * Spinner component for loading state
 */
const Spinner: React.FC<{ className?: string }> = ({ className = '' }) => (
  <motion.div
    className={`inline-block h-4 w-4 animate-spin rounded-full border-2 border-solid border-current border-r-transparent ${className}`}
    initial={{ opacity: 0 }}
    animate={{ opacity: 1 }}
    exit={{ opacity: 0 }}
    transition={{ duration: 0.15 }}
  />
);

export interface ButtonProps
  extends Omit<HTMLMotionProps<'button'>, 'size'>,
    VariantProps<typeof buttonVariants> {
  /**
   * Whether the button is in a loading state
   */
  isLoading?: boolean;
  /**
   * Text to display when loading (replaces children)
   */
  loadingText?: string;
  /**
   * Icon to display before the button text
   */
  leftIcon?: React.ReactNode;
  /**
   * Icon to display after the button text
   */
  rightIcon?: React.ReactNode;
}

/**
 * Button component with variants, animations, and accessibility features
 *
 * @example
 * ```tsx
 * <Button variant="primary" size="lg" onClick={handleClick}>
 *   Click me
 * </Button>
 *
 * <Button variant="outline" isLoading loadingText="Saving...">
 *   Save
 * </Button>
 * ```
 */
export const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      className,
      variant,
      size,
      fullWidth,
      isLoading = false,
      loadingText,
      leftIcon,
      rightIcon,
      disabled,
      children,
      ...props
    },
    ref
  ) => {
    const isDisabled = disabled || isLoading;

    const motionProps = !isDisabled
      ? {
          whileHover: { scale: 1.02 },
          whileTap: { scale: 0.98 },
          transition: { type: 'spring' as const, stiffness: 400, damping: 17 },
        }
      : {};

    return (
      <motion.button
        ref={ref}
        className={buttonVariants({ variant, size, fullWidth, className })}
        disabled={isDisabled}
        {...motionProps}
        {...props}
      >
        {isLoading && <Spinner />}
        {!isLoading && leftIcon && (
          <motion.span
            initial={{ opacity: 0, x: -4 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.2 }}
          >
            {leftIcon}
          </motion.span>
        )}

        <motion.span
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.2 }}
        >
          {isLoading && loadingText ? loadingText : children}
        </motion.span>

        {!isLoading && rightIcon && (
          <motion.span
            initial={{ opacity: 0, x: 4 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.2 }}
          >
            {rightIcon}
          </motion.span>
        )}
      </motion.button>
    );
  }
);

Button.displayName = 'Button';
* as React from 'react';
import
// Minimal variant system (kept primary + emphasis only for debug)'uecle';//ULTRAMINIMALBUTTON(deu ebuldsnnl)//Ifuilllrortold lines(e.g., line ~195)cachisal.
ior*Rcfomect  Reac.ButtonAtibuteHTMLBElment      Wh?:b;    variant?: 'primary' | 'emphasis';
    size?: 'sm' | 'md' | 'lg';
  }  sizeMap: Record<string, string> = {
    sm: 'h-8 px-3 text-sm',
    md: 'h-10 px-4 text-sm',
    lg: 'h-11 px-6 text-base',
  };

  const
       = '' = 'primary' = 'md'chidr

       base = 'inline-flextems-center jutify-center rounded-lg font-medum tranition-ll focus-visible:outline-none focus-visible:ring-2 focus-visi:ring-bran-mint:opacity-50dble:pote-evns-noe';cns varaCls =vaiant == 'mphasis'   ?'bg-grdin-o-r frm-brad-mto-bd-yow text-whehdow'
  :'bg-bran-mint text-whte hover:bg-brn-g';constszeCs  szeMap[z]?izeMpm;constidtCs  fullWth'w-ful'  '';
  retr (<bu refref}className={[bas,vaatClszCl,widthClsclassNe].jo('')disabled={disabled||isLoading}>?(
            span className='inline-block h-4 w-4 animate-sn rouded-full border-2 border-solid border-curret bordr-r-transpaent'
          )(  loadingText ??
          )}      </button>
;

  );

  Button.displayName = 'Button';
  export default Button;
