'use client';

import { motion } from 'framer-motion';
import React from 'react';
import { tv, type VariantProps } from 'tailwind-variants';

/**
 * Input component variants using tailwind-variants
 */
export const inputVariants = tv({
  base: [
    'flex w-full rounded-lg border transition-all duration-200',
    'focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2',
    'disabled:cursor-not-allowed disabled:opacity-50',
    'relative',
  ],
  variants: {
    variant: {
      default: [
        'border-border bg-surface',
        'hover:border-brand-mint/60',
        'focus-within:border-brand-mint',
        'dark:border-border dark:bg-surface',
      ],
      filled: [
        'border-transparent bg-elevated',
        'hover:bg-surface',
        'focus-within:bg-surface focus-within:border-brand-mint',
        'dark:bg-elevated dark:hover:bg-surface',
      ],
      outline: [
        'border-brand-mint bg-transparent',
        'hover:border-brand-sage',
        'focus-within:border-brand-mint focus-within:bg-surface/50',
        'dark:border-brand-mint dark:hover:border-brand-sage',
      ],
    },
    size: {
      sm: 'h-8 text-sm',
      md: 'h-10 text-sm',
      lg: 'h-11 text-base',
      xl: 'h-12 text-base',
    },
    state: {
      default: '',
      error: [
        'border-brand-coral focus-within:border-brand-coral',
        'focus-within:ring-brand-coral/20',
      ],
      success: [
        'border-green-500 focus-within:border-green-500',
        'focus-within:ring-green-500/20',
      ],
      warning: [
        'border-brand-yellow focus-within:border-brand-yellow',
        'focus-within:ring-brand-yellow/20',
      ],
    },
  },
  defaultVariants: {
    variant: 'default',
    size: 'md',
    state: 'default',
  },
});

const inputFieldVariants = tv({
  base: [
    'flex-1 bg-transparent px-3 py-2 text-primary placeholder:text-muted',
    'focus:outline-none',
    'disabled:cursor-not-allowed',
  ],
  variants: {
    hasLeftIcon: {
      true: 'pl-10',
    },
    hasRightIcon: {
      true: 'pr-10',
    },
  },
});

export interface InputProps
  extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size'>,
    VariantProps<typeof inputVariants> {
  /**
   * Icon to display on the left side of the input
   */
  leftIcon?: React.ReactNode;
  /**
   * Icon to display on the right side of the input
   */
  rightIcon?: React.ReactNode;
  /**
   * Error message to display below the input
   */
  error?: string;
  /**
   * Success message to display below the input
   */
  success?: string;
  /**
   * Warning message to display below the input
   */
  warning?: string;
  /**
   * Helper text to display below the input
   */
  helperText?: string;
  /**
   * Label for the input
   */
  label?: string;
  /**
   * Whether the input is required
   */
  required?: boolean;
  /**
   * Container class name
   */
  containerClassName?: string;
}

/**
 * Input component with variants, states, icons, and animations
 *
 * @example
 * ```tsx
 * <Input
 *   label="Email"
 *   placeholder="Enter your email"
 *   leftIcon={<EmailIcon />}
 *   required
 * />
 *
 * <Input
 *   variant="filled"
 *   state="error"
 *   error="This field is required"
 * />
 * ```
 */
export const Input = React.forwardRef<HTMLInputElement, InputProps>(
  (
    {
      className,
      containerClassName,
      variant,
      size,
      state,
      leftIcon,
      rightIcon,
      error,
      success,
      warning,
      helperText,
      label,
      required,
      disabled,
      id,
      ...props
    },
    ref
  ) => {
    const inputId = id || React.useId();
    const hasLeftIcon = Boolean(leftIcon);
    const hasRightIcon = Boolean(rightIcon);

    // Determine the actual state based on props
    const actualState = error
      ? 'error'
      : success
        ? 'success'
        : warning
          ? 'warning'
          : state;

    // Get the message to display
    const message = error || success || warning || helperText;
    const messageType = error
      ? 'error'
      : success
        ? 'success'
        : warning
          ? 'warning'
          : 'helper';

    return (
      <div className={`space-y-2 ${containerClassName || ''}`}>
        {label && (
          <motion.label
            htmlFor={inputId}
            className='block text-sm font-medium text-primary'
            initial={{ opacity: 0, y: -4 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.2 }}
          >
            {label}
            {required && <span className='ml-1 text-brand-coral'>*</span>}
          </motion.label>
        )}

        <motion.div
          className={inputVariants({
            variant,
            size,
            state: actualState,
            className,
          })}
          initial={{ opacity: 0, scale: 0.98 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.2 }}
        >
          {leftIcon && (
            <motion.div
              className='absolute left-3 top-1/2 -translate-y-1/2 text-muted'
              initial={{ opacity: 0, x: -4 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.2, delay: 0.1 }}
            >
              {leftIcon}
            </motion.div>
          )}

          <input
            ref={ref}
            id={inputId}
            className={inputFieldVariants({ hasLeftIcon, hasRightIcon })}
            disabled={disabled}
            {...props}
          />

          {rightIcon && (
            <motion.div
              className='absolute right-3 top-1/2 -translate-y-1/2 text-muted'
              initial={{ opacity: 0, x: 4 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.2, delay: 0.1 }}
            >
              {rightIcon}
            </motion.div>
          )}
        </motion.div>

        {message && (
          <motion.p
            className={`text-xs ${
              messageType === 'error'
                ? 'text-brand-coral'
                : messageType === 'success'
                  ? 'text-green-600 dark:text-green-400'
                  : messageType === 'warning'
                    ? 'text-brand-yellow'
                    : 'text-muted'
            }`}
            initial={{ opacity: 0, y: -4 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.2 }}
          >
            {message}
          </motion.p>
        )}
      </div>
    );
  }
);

Input.displayName = 'Input';
