'use client';

import { motion } from 'framer-motion';
import React from 'react';
import { tv, type VariantProps } from 'tailwind-variants';

/**
 * Avatar component variants using tailwind-variants
 */
export const avatarVariants = tv({
  base: [
    'relative inline-flex items-center justify-center overflow-hidden',
    'bg-surface border border-border text-primary font-medium',
    'select-none shrink-0',
  ],
  variants: {
    size: {
      xs: 'h-6 w-6 text-xs',
      sm: 'h-8 w-8 text-sm',
      md: 'h-10 w-10 text-sm',
      lg: 'h-12 w-12 text-base',
      xl: 'h-16 w-16 text-lg',
      '2xl': 'h-20 w-20 text-xl',
    },
    shape: {
      circle: 'rounded-full',
      square: 'rounded-lg',
    },
    variant: {
      default: 'bg-surface border-border',
      primary: 'bg-brand-mint text-white border-brand-mint',
      secondary: 'bg-brand-sage text-white border-brand-sage',
      outline: 'bg-transparent border-2 border-border',
    },
  },
  defaultVariants: {
    size: 'md',
    shape: 'circle',
    variant: 'default',
  },
});

const avatarImageVariants = tv({
  base: 'h-full w-full object-cover',
  variants: {
    shape: {
      circle: 'rounded-full',
      square: 'rounded-lg',
    },
  },
});

export interface AvatarProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof avatarVariants> {
  /**
   * Image source URL
   */
  src?: string;
  /**
   * Alt text for the image
   */
  alt?: string;
  /**
   * Fallback text (usually initials)
   */
  fallback?: string;
  /**
   * Icon to display when no image or fallback
   */
  icon?: React.ReactNode;
  /**
   * Whether to animate the avatar entrance
   */
  animate?: boolean;
  /**
   * Status indicator
   */
  status?: 'online' | 'offline' | 'away' | 'busy';
  /**
   * Whether the avatar is clickable
   */
  interactive?: boolean;
}

/**
 * Get initials from a name string
 */
function getInitials(name: string): string {
  return name
    .split(' ')
    .map((part) => part.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2);
}

/**
 * Status indicator component
 */
const StatusIndicator: React.FC<{
  status: NonNullable<AvatarProps['status']>;
  size: string;
}> = ({ status, size }) => {
  const statusColors = {
    online: 'bg-green-500',
    offline: 'bg-gray-400',
    away: 'bg-yellow-500',
    busy: 'bg-red-500',
  };

  const statusSizes = {
    xs: 'h-1.5 w-1.5',
    sm: 'h-2 w-2',
    md: 'h-2.5 w-2.5',
    lg: 'h-3 w-3',
    xl: 'h-4 w-4',
    '2xl': 'h-5 w-5',
  };

  return (
    <span
      className={`absolute bottom-0 right-0 block rounded-full border-2 border-white dark:border-gray-900 ${statusColors[status]} ${statusSizes[size as keyof typeof statusSizes]}`}
      role='img'
      aria-label={`Status: ${status}`}
    />
  );
};

/**
 * Avatar component for displaying user profile pictures or initials
 *
 * @example
 * ```tsx
 * <Avatar src="/user.jpg" alt="John Doe" />
 * <Avatar fallback="JD" status="online" />
 * <Avatar icon={<UserIcon />} size="lg" />
 * ```
 */
export const Avatar = React.forwardRef<HTMLDivElement, AvatarProps>(
  (
    {
      className,
      size,
      shape,
      variant,
      src,
      alt,
      fallback,
      icon,
      animate = true,
      status,
      interactive,
      onClick,
      ...props
    },
    ref
  ) => {
    const [imageError, setImageError] = React.useState(false);
    const [imageLoaded, setImageLoaded] = React.useState(false);

    const isInteractive = interactive || Boolean(onClick);

    // Reset image error when src changes
    React.useEffect(() => {
      setImageError(false);
      setImageLoaded(false);
    }, [src]);

    const renderContent = () => {
      // Show image if available and not errored
      if (src && !imageError) {
        return (
          <img
            src={src}
            alt={alt || 'Avatar'}
            className={avatarImageVariants({ shape })}
            onError={() => setImageError(true)}
            onLoad={() => setImageLoaded(true)}
            style={{
              opacity: imageLoaded ? 1 : 0,
              transition: 'opacity 0.2s ease-in-out',
            }}
          />
        );
      }

      // Show fallback text (initials)
      if (fallback) {
        const initials = fallback.length > 2 ? getInitials(fallback) : fallback;
        return <span className='text-current'>{initials}</span>;
      }

      // Show icon
      if (icon) {
        return <span className='text-current'>{icon}</span>;
      }

      // Default user icon
      return (
        <svg
          className='h-3/5 w-3/5 text-muted'
          fill='currentColor'
          viewBox='0 0 24 24'
        >
          <path d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z' />
        </svg>
      );
    };

    const avatarElement = (
      <div
        ref={ref}
        className={avatarVariants({
          size,
          shape,
          variant,
          className: `${isInteractive ? 'cursor-pointer hover:opacity-80 transition-opacity' : ''} ${className || ''}`,
        })}
        onClick={onClick}
        {...props}
      >
        {renderContent()}
        {status && <StatusIndicator status={status} size={size || 'md'} />}
      </div>
    );

    if (animate) {
      return (
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.2 }}
          whileHover={isInteractive ? { scale: 1.05 } : undefined}
          whileTap={isInteractive ? { scale: 0.95 } : undefined}
        >
          {avatarElement}
        </motion.div>
      );
    }

    return avatarElement;
  }
);

Avatar.displayName = 'Avatar';

/**
 * Avatar group component for displaying multiple avatars
 */
export interface AvatarGroupProps extends React.HTMLAttributes<HTMLDivElement> {
  /**
   * Maximum number of avatars to show before showing "+X more"
   */
  max?: number;
  /**
   * Size of the avatars
   */
  size?: AvatarProps['size'];
  /**
   * Shape of the avatars
   */
  shape?: AvatarProps['shape'];
  /**
   * Spacing between avatars
   */
  spacing?: 'tight' | 'normal' | 'relaxed';
}

export const AvatarGroup: React.FC<AvatarGroupProps> = ({
  children,
  max = 5,
  size = 'md',
  shape = 'circle',
  spacing = 'normal',
  className,
  ...props
}) => {
  const childrenArray = React.Children.toArray(children);
  const visibleChildren = childrenArray.slice(0, max);
  const remainingCount = Math.max(0, childrenArray.length - max);

  const spacingClasses = {
    tight: '-space-x-1',
    normal: '-space-x-2',
    relaxed: '-space-x-1',
  };

  return (
    <div
      className={`flex items-center ${spacingClasses[spacing]} ${className || ''}`}
      {...props}
    >
      {visibleChildren.map((child, index) =>
        React.cloneElement(child as React.ReactElement, {
          key: index,
          size,
          shape,
          className: 'ring-2 ring-white dark:ring-gray-900',
        })
      )}
      {remainingCount > 0 && (
        <Avatar
          size={size}
          shape={shape}
          fallback={`+${remainingCount}`}
          variant='outline'
          className='ring-2 ring-white dark:ring-gray-900'
        />
      )}
    </div>
  );
};

AvatarGroup.displayName = 'AvatarGroup';
