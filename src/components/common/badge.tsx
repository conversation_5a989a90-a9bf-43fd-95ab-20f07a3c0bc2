'use client';

import { motion } from 'framer-motion';
import React from 'react';
import { tv, type VariantProps } from 'tailwind-variants';

/**
 * Badge component variants using tailwind-variants
 */
export const badgeVariants = tv({
  base: [
    'inline-flex items-center gap-1 rounded-full px-2.5 py-0.5 text-xs font-medium transition-all duration-200',
    'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2',
  ],
  variants: {
    variant: {
      default: [
        'bg-surface border border-border text-primary',
        'hover:bg-elevated',
        'dark:bg-surface dark:border-border dark:text-primary',
      ],
      primary: [
        'bg-brand-mint text-white',
        'hover:bg-brand-sage',
        'dark:bg-brand-mint dark:text-white',
      ],
      secondary: [
        'bg-brand-sage/20 text-brand-mint border border-brand-sage/30',
        'hover:bg-brand-sage/30',
        'dark:bg-brand-sage/20 dark:text-brand-mint',
      ],
      success: [
        'bg-green-100 text-green-800 border border-green-200',
        'hover:bg-green-200',
        'dark:bg-green-900/20 dark:text-green-400 dark:border-green-800',
      ],
      warning: [
        'bg-brand-yellow/20 text-yellow-800 border border-brand-yellow/30',
        'hover:bg-brand-yellow/30',
        'dark:bg-yellow-900/20 dark:text-yellow-400 dark:border-yellow-800',
      ],
      error: [
        'bg-brand-coral/20 text-red-800 border border-brand-coral/30',
        'hover:bg-brand-coral/30',
        'dark:bg-red-900/20 dark:text-red-400 dark:border-red-800',
      ],
      outline: [
        'border border-border text-primary bg-transparent',
        'hover:bg-surface',
        'dark:border-border dark:text-primary',
      ],
    },
    size: {
      sm: 'px-2 py-0.5 text-xs',
      md: 'px-2.5 py-0.5 text-xs',
      lg: 'px-3 py-1 text-sm',
    },
    interactive: {
      true: 'cursor-pointer hover:scale-105 active:scale-95',
    },
  },
  defaultVariants: {
    variant: 'default',
    size: 'md',
  },
});

export interface BadgeProps
  extends React.HTMLAttributes<HTMLSpanElement>,
    VariantProps<typeof badgeVariants> {
  /**
   * Icon to display before the badge text
   */
  icon?: React.ReactNode;
  /**
   * Icon to display after the badge text
   */
  endIcon?: React.ReactNode;
  /**
   * Whether the badge is removable (shows close button)
   */
  removable?: boolean;
  /**
   * Callback when remove button is clicked
   */
  onRemove?: () => void;
  /**
   * Whether to animate the badge entrance
   */
  animate?: boolean;
}

/**
 * Badge component for displaying status, categories, or labels
 * 
 * @example
 * ```tsx
 * <Badge variant="primary">New</Badge>
 * <Badge variant="success" icon={<CheckIcon />}>Completed</Badge>
 * <Badge variant="warning" removable onRemove={handleRemove}>
 *   Draft
 * </Badge>
 * ```
 */
export const Badge = React.forwardRef<HTMLSpanElement, BadgeProps>(
  (
    {
      className,
      variant,
      size,
      interactive,
      icon,
      endIcon,
      removable,
      onRemove,
      animate = true,
      children,
      onClick,
      ...props
    },
    ref
  ) => {
    const isInteractive = interactive || Boolean(onClick) || removable;

    const badgeContent = (
      <>
        {icon && (
          <span className="inline-flex items-center">
            {icon}
          </span>
        )}
        {children && <span>{children}</span>}
        {endIcon && (
          <span className="inline-flex items-center">
            {endIcon}
          </span>
        )}
        {removable && (
          <button
            type="button"
            onClick={(e) => {
              e.stopPropagation();
              onRemove?.();
            }}
            className="ml-1 inline-flex items-center justify-center w-3 h-3 rounded-full hover:bg-black/10 dark:hover:bg-white/10 transition-colors"
            aria-label="Remove"
          >
            <svg
              className="w-2 h-2"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        )}
      </>
    );

    if (animate) {
      return (
        <motion.span
          ref={ref}
          className={badgeVariants({
            variant,
            size,
            interactive: isInteractive,
            className,
          })}
          onClick={onClick}
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.8 }}
          transition={{ duration: 0.2 }}
          whileHover={isInteractive ? { scale: 1.05 } : undefined}
          whileTap={isInteractive ? { scale: 0.95 } : undefined}
          {...props}
        >
          {badgeContent}
        </motion.span>
      );
    }

    return (
      <span
        ref={ref}
        className={badgeVariants({
          variant,
          size,
          interactive: isInteractive,
          className,
        })}
        onClick={onClick}
        {...props}
      >
        {badgeContent}
      </span>
    );
  }
);

Badge.displayName = 'Badge';
