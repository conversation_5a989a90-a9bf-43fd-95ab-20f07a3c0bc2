'use client';

import { motion, AnimatePresence } from 'framer-motion';
import React from 'react';
import { tv, type VariantProps } from 'tailwind-variants';

/**
 * Modal component variants using tailwind-variants
 */
export const modalVariants = tv({
  base: [
    'relative bg-surface border border-border rounded-lg shadow-xl',
    'w-full max-h-[90vh] overflow-hidden',
    'focus:outline-none',
  ],
  variants: {
    size: {
      sm: 'max-w-sm',
      md: 'max-w-md',
      lg: 'max-w-lg',
      xl: 'max-w-xl',
      '2xl': 'max-w-2xl',
      '3xl': 'max-w-3xl',
      '4xl': 'max-w-4xl',
      full: 'max-w-full mx-4',
    },
  },
  defaultVariants: {
    size: 'md',
  },
});

export interface ModalProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof modalVariants> {
  /**
   * Whether the modal is open
   */
  open?: boolean;
  /**
   * Callback when the modal should close
   */
  onClose?: () => void;
  /**
   * Whether clicking the overlay closes the modal
   */
  closeOnOverlayClick?: boolean;
  /**
   * Whether pressing Escape closes the modal
   */
  closeOnEscape?: boolean;
  /**
   * Whether to show the close button
   */
  showCloseButton?: boolean;
  /**
   * Custom close button
   */
  closeButton?: React.ReactNode;
  /**
   * Modal title for accessibility
   */
  title?: string;
  /**
   * Modal description for accessibility
   */
  description?: string;
}

/**
 * Modal component for displaying content in an overlay
 * 
 * @example
 * ```tsx
 * <Modal open={isOpen} onClose={() => setIsOpen(false)} title="Confirm Action">
 *   <ModalHeader>
 *     <ModalTitle>Delete Item</ModalTitle>
 *   </ModalHeader>
 *   <ModalBody>
 *     Are you sure you want to delete this item?
 *   </ModalBody>
 *   <ModalFooter>
 *     <Button variant="outline" onClick={() => setIsOpen(false)}>Cancel</Button>
 *     <Button variant="destructive">Delete</Button>
 *   </ModalFooter>
 * </Modal>
 * ```
 */
export const Modal = React.forwardRef<HTMLDivElement, ModalProps>(
  (
    {
      className,
      size,
      open = false,
      onClose,
      closeOnOverlayClick = true,
      closeOnEscape = true,
      showCloseButton = true,
      closeButton,
      title,
      description,
      children,
      ...props
    },
    ref
  ) => {
    // Handle escape key
    React.useEffect(() => {
      if (!closeOnEscape || !open) return;

      const handleEscape = (event: KeyboardEvent) => {
        if (event.key === 'Escape') {
          onClose?.();
        }
      };

      document.addEventListener('keydown', handleEscape);
      return () => document.removeEventListener('keydown', handleEscape);
    }, [closeOnEscape, open, onClose]);

    // Prevent body scroll when modal is open
    React.useEffect(() => {
      if (open) {
        document.body.style.overflow = 'hidden';
      } else {
        document.body.style.overflow = '';
      }

      return () => {
        document.body.style.overflow = '';
      };
    }, [open]);

    return (
      <AnimatePresence>
        {open && (
          <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
            {/* Overlay */}
            <motion.div
              className="absolute inset-0 bg-black/50 backdrop-blur-sm"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.2 }}
              onClick={closeOnOverlayClick ? onClose : undefined}
            />

            {/* Modal */}
            <motion.div
              ref={ref}
              className={modalVariants({ size, className })}
              initial={{ opacity: 0, scale: 0.95, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.95, y: 20 }}
              transition={{ duration: 0.2 }}
              role="dialog"
              aria-modal="true"
              aria-labelledby={title ? 'modal-title' : undefined}
              aria-describedby={description ? 'modal-description' : undefined}
              {...props}
            >
              {/* Close button */}
              {showCloseButton && (
                <button
                  type="button"
                  onClick={onClose}
                  className="absolute top-4 right-4 p-1 rounded-md text-muted hover:text-primary hover:bg-surface transition-colors z-10"
                  aria-label="Close modal"
                >
                  {closeButton || (
                    <svg
                      className="w-5 h-5"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M6 18L18 6M6 6l12 12"
                      />
                    </svg>
                  )}
                </button>
              )}

              {/* Hidden title and description for accessibility */}
              {title && (
                <h2 id="modal-title" className="sr-only">
                  {title}
                </h2>
              )}
              {description && (
                <p id="modal-description" className="sr-only">
                  {description}
                </p>
              )}

              {children}
            </motion.div>
          </div>
        )}
      </AnimatePresence>
    );
  }
);

Modal.displayName = 'Modal';

/**
 * Modal header component
 */
export interface ModalHeaderProps extends React.HTMLAttributes<HTMLDivElement> {}

export const ModalHeader: React.FC<ModalHeaderProps> = ({
  className,
  children,
  ...props
}) => (
  <div
    className={`px-6 py-4 border-b border-border ${className || ''}`}
    {...props}
  >
    {children}
  </div>
);

ModalHeader.displayName = 'ModalHeader';

/**
 * Modal title component
 */
export interface ModalTitleProps extends React.HTMLAttributes<HTMLHeadingElement> {
  as?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6';
}

export const ModalTitle: React.FC<ModalTitleProps> = ({
  as: Component = 'h3',
  className,
  children,
  ...props
}) => (
  <Component
    className={`text-lg font-semibold text-primary ${className || ''}`}
    {...props}
  >
    {children}
  </Component>
);

ModalTitle.displayName = 'ModalTitle';

/**
 * Modal body component
 */
export interface ModalBodyProps extends React.HTMLAttributes<HTMLDivElement> {}

export const ModalBody: React.FC<ModalBodyProps> = ({
  className,
  children,
  ...props
}) => (
  <div
    className={`px-6 py-4 overflow-y-auto ${className || ''}`}
    {...props}
  >
    {children}
  </div>
);

ModalBody.displayName = 'ModalBody';

/**
 * Modal footer component
 */
export interface ModalFooterProps extends React.HTMLAttributes<HTMLDivElement> {
  /**
   * Alignment of footer content
   */
  alignment?: 'left' | 'center' | 'right' | 'between';
}

export const ModalFooter: React.FC<ModalFooterProps> = ({
  className,
  alignment = 'right',
  children,
  ...props
}) => {
  const alignmentClasses = {
    left: 'justify-start',
    center: 'justify-center',
    right: 'justify-end',
    between: 'justify-between',
  };

  return (
    <div
      className={`px-6 py-4 border-t border-border flex gap-3 ${alignmentClasses[alignment]} ${className || ''}`}
      {...props}
    >
      {children}
    </div>
  );
};

ModalFooter.displayName = 'ModalFooter';
