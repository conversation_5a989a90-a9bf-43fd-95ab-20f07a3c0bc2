/**
 * Component prop type definitions
 *
 * This file contains all the prop interfaces for UI components,
 * extracted from individual component files for better organization.
 */

import type { HTMLMotionProps } from 'framer-motion';
import type React from 'react';

import type {
  IconProps,
  LoadingProps,
  WithChildren,
  WithClassName,
} from './common';
import type {
  AvatarShape,
  AvatarSize,
  AvatarVariant,
  BadgeSize,
  BadgeVariant,
  ButtonSize,
  ButtonVariant,
  CardVariant,
  ContainerSize,
  GridColumns,
  InputSize,
  InputVariant,
  SpinnerSize,
  SpinnerVariant,
  StackAlign,
  StackDirection,
  StackJustify,
  StatusType,
} from './variants';

/**
 * Button component props
 * Generic interface that can be extended with specific variant props
 */
export interface BaseButtonProps
  extends Omit<HTMLMotionProps<'button'>, 'size'>,
    LoadingProps,
    IconProps,
    WithClassName {
  /** Button visual variant */
  variant?: ButtonVariant;
  /** Button size */
  size?: ButtonSize;
  /** Whether button takes full width */
  fullWidth?: boolean;
  /** Icon to display before the button text */
  leftIcon?: React.ReactNode;
  /** Icon to display after the button text */
  rightIcon?: React.ReactNode;
}

/**
 * Button props that can be extended with variant props from tailwind-variants
 */
export type ButtonProps<T = {}> = BaseButtonProps & T;

/**
 * Input component props
 */
export interface InputProps
  extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size'>,
    WithClassName {
  /** Container className for styling wrapper */
  containerClassName?: string;
  /** Input visual variant */
  variant?: InputVariant;
  /** Input size */
  size?: InputSize;
  /** Input state for validation styling */
  state?: 'default' | 'error' | 'success' | 'warning';
  /** Icon to display on the left */
  leftIcon?: React.ReactNode;
  /** Icon to display on the right */
  rightIcon?: React.ReactNode;
  /** Error message to display */
  error?: string;
  /** Success message to display */
  success?: string;
  /** Warning message to display */
  warning?: string;
  /** Helper text to display */
  helperText?: string;
  /** Input label */
  label?: string;
}

/**
 * Card component props
 */
export interface CardProps
  extends React.HTMLAttributes<HTMLDivElement>,
    WithChildren,
    WithClassName {
  /** Card visual variant */
  variant?: CardVariant;
  /** Whether card is interactive/clickable */
  interactive?: boolean;
  /** Whether to animate card entrance */
  animate?: boolean;
}

/**
 * Card sub-component props
 */
export interface CardHeaderProps
  extends React.HTMLAttributes<HTMLDivElement>,
    WithChildren,
    WithClassName {}
export interface CardTitleProps
  extends React.HTMLAttributes<HTMLHeadingElement>,
    WithChildren,
    WithClassName {}
export interface CardDescriptionProps
  extends React.HTMLAttributes<HTMLParagraphElement>,
    WithChildren,
    WithClassName {}
export interface CardContentProps
  extends React.HTMLAttributes<HTMLDivElement>,
    WithChildren,
    WithClassName {}
export interface CardFooterProps
  extends React.HTMLAttributes<HTMLDivElement>,
    WithChildren,
    WithClassName {}
export interface CardImageProps
  extends React.ImgHTMLAttributes<HTMLImageElement>,
    WithClassName {
  /** Whether to animate image loading */
  animate?: boolean;
}

/**
 * Badge component props
 */
export interface BadgeProps
  extends React.HTMLAttributes<HTMLSpanElement>,
    WithChildren,
    WithClassName {
  /** Badge visual variant */
  variant?: BadgeVariant;
  /** Badge size */
  size?: BadgeSize;
  /** Whether badge is interactive */
  interactive?: boolean;
  /** Icon to display before the badge text */
  icon?: React.ReactNode;
  /** Icon to display after the badge text */
  endIcon?: React.ReactNode;
  /** Whether the badge is removable (shows close button) */
  removable?: boolean;
  /** Callback when remove button is clicked */
  onRemove?: () => void;
  /** Whether to animate the badge entrance */
  animate?: boolean;
}

/**
 * Avatar component props
 */
export interface AvatarProps
  extends React.HTMLAttributes<HTMLDivElement>,
    WithClassName {
  /** Avatar size */
  size?: AvatarSize;
  /** Avatar shape */
  shape?: AvatarShape;
  /** Avatar visual variant */
  variant?: AvatarVariant;
  /** Image source URL */
  src?: string;
  /** Alt text for the image */
  alt?: string;
  /** Fallback text (usually initials) */
  fallback?: string;
  /** Icon to display when no image or fallback */
  icon?: React.ReactNode;
  /** Whether to animate the avatar entrance */
  animate?: boolean;
  /** Status indicator */
  status?: StatusType;
  /** Whether the avatar is clickable */
  interactive?: boolean;
}

/**
 * Avatar group component props
 */
export interface AvatarGroupProps
  extends React.HTMLAttributes<HTMLDivElement>,
    WithChildren,
    WithClassName {
  /** Maximum number of avatars to show before showing "+X more" */
  max?: number;
  /** Size of the avatars */
  size?: AvatarSize;
  /** Shape of the avatars */
  shape?: AvatarShape;
  /** Spacing between avatars */
  spacing?: 'tight' | 'normal' | 'relaxed';
}

/**
 * Spinner component props
 */
export interface SpinnerProps
  extends React.HTMLAttributes<HTMLDivElement>,
    WithClassName {
  /** Spinner size */
  size?: SpinnerSize;
  /** Spinner visual variant */
  variant?: SpinnerVariant;
  /** Loading text to display */
  label?: string;
}

/**
 * Spinner overlay component props
 */
export interface SpinnerOverlayProps extends SpinnerProps, WithChildren {
  /** Whether the overlay is visible */
  visible?: boolean;
  /** Overlay background opacity */
  opacity?: number;
  /** Whether to blur the background content */
  blur?: boolean;
}

/**
 * Container component props
 */
export interface ContainerProps
  extends React.HTMLAttributes<HTMLDivElement>,
    WithChildren,
    WithClassName {
  /** Container size */
  size?: ContainerSize;
  /** Whether to center the container */
  centered?: boolean;
}

/**
 * Stack component props
 */
export interface StackProps
  extends React.HTMLAttributes<HTMLDivElement>,
    WithChildren,
    WithClassName {
  /** Stack direction */
  direction?: StackDirection;
  /** Item alignment */
  align?: StackAlign;
  /** Content justification */
  justify?: StackJustify;
  /** Spacing between items */
  spacing?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';
  /** Whether to wrap items */
  wrap?: boolean;
}

/**
 * Grid component props
 */
export interface GridProps
  extends React.HTMLAttributes<HTMLDivElement>,
    WithChildren,
    WithClassName {
  /** Number of columns */
  columns?: GridColumns;
  /** Gap between grid items */
  gap?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';
  /** Responsive columns */
  responsive?: {
    sm?: GridColumns;
    md?: GridColumns;
    lg?: GridColumns;
    xl?: GridColumns;
  };
}

/**
 * Flex component props
 */
export interface FlexProps
  extends React.HTMLAttributes<HTMLDivElement>,
    WithChildren,
    WithClassName {
  /** Flex direction */
  direction?: 'row' | 'column' | 'row-reverse' | 'column-reverse';
  /** Item alignment */
  align?: 'start' | 'center' | 'end' | 'stretch' | 'baseline';
  /** Content justification */
  justify?: 'start' | 'center' | 'end' | 'between' | 'around' | 'evenly';
  /** Whether to wrap items */
  wrap?: boolean;
  /** Gap between items */
  gap?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';
}

/**
 * Center component props
 */
export interface CenterProps
  extends React.HTMLAttributes<HTMLDivElement>,
    WithChildren,
    WithClassName {
  /** Whether to center inline content */
  inline?: boolean;
}

/**
 * Spacer component props
 */
export interface SpacerProps
  extends React.HTMLAttributes<HTMLDivElement>,
    WithClassName {
  /** Spacer size */
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';
}

/**
 * Divider component props
 */
export interface DividerProps
  extends React.HTMLAttributes<HTMLHRElement>,
    WithClassName {
  /** Divider orientation */
  orientation?: 'horizontal' | 'vertical';
  /** Divider variant */
  variant?: 'solid' | 'dashed' | 'dotted';
  /** Label to display on the divider */
  label?: string;
}

/**
 * Form component props
 */
export interface FormProps
  extends React.FormHTMLAttributes<HTMLFormElement>,
    WithChildren,
    WithClassName {
  /** Form spacing variant */
  spacing?: FormSpacing;
}

/**
 * Form field component props
 */
export interface FormFieldProps
  extends React.HTMLAttributes<HTMLDivElement>,
    WithChildren,
    WithClassName {
  /** Field spacing variant */
  spacing?: FormSpacing;
}

/**
 * Form label component props
 */
export interface FormLabelProps
  extends React.LabelHTMLAttributes<HTMLLabelElement>,
    WithChildren,
    WithClassName {
  /** Whether the field is required */
  required?: boolean;
  /** Custom required indicator */
  requiredIndicator?: React.ReactNode;
}

/**
 * Form error component props
 */
export interface FormErrorProps
  extends React.HTMLAttributes<HTMLParagraphElement>,
    WithChildren,
    WithClassName {
  /** Error message to display */
  error?: string;
}

/**
 * Form help component props
 */
export interface FormHelpProps
  extends React.HTMLAttributes<HTMLParagraphElement>,
    WithChildren,
    WithClassName {
  /** Help text to display */
  text?: string;
}

/**
 * Form actions component props
 */
export interface FormActionsProps
  extends React.HTMLAttributes<HTMLDivElement>,
    WithChildren,
    WithClassName {
  /** Actions alignment */
  align?: 'left' | 'center' | 'right';
  /** Spacing between actions */
  spacing?: 'sm' | 'md' | 'lg';
}

/**
 * Modal component props
 */
export interface ModalProps
  extends React.HTMLAttributes<HTMLDivElement>,
    WithChildren,
    WithClassName {
  /** Whether the modal is open */
  isOpen?: boolean;
  /** Callback when modal should close */
  onClose?: () => void;
  /** Modal size */
  size?: ModalSize;
  /** Whether to close on overlay click */
  closeOnOverlayClick?: boolean;
  /** Whether to close on escape key */
  closeOnEscape?: boolean;
  /** Whether to trap focus within modal */
  trapFocus?: boolean;
  /** Initial focus element selector */
  initialFocus?: string;
}

/**
 * Modal sub-component props
 */
export interface ModalHeaderProps
  extends React.HTMLAttributes<HTMLDivElement>,
    WithChildren,
    WithClassName {}
export interface ModalTitleProps
  extends React.HTMLAttributes<HTMLHeadingElement>,
    WithChildren,
    WithClassName {}
export interface ModalBodyProps
  extends React.HTMLAttributes<HTMLDivElement>,
    WithChildren,
    WithClassName {}
export interface ModalFooterProps
  extends React.HTMLAttributes<HTMLDivElement>,
    WithChildren,
    WithClassName {
  /** Footer actions alignment */
  align?: 'left' | 'center' | 'right';
}
